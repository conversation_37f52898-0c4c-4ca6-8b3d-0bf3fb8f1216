import React, { useState, useCallback, useImperativeHandle, forwardRef, useEffect } from 'react';
import { useUser, useAuth, useClerk } from '@clerk/nextjs';
import { MultiFieldInput } from '../../MultiFieldInput/MultiFieldInput';
import { MultiFieldInputType, AddressFields } from '../../MultiFieldInput/MultiFieldInput.types';
import { useGuestConversion } from '@/hooks/useGuestConversion';
import styles from './FieldComponents.module.scss';

interface EmailFieldProps {
  initialValue: string;
  onSave?: (value: string) => void;
  onEditStart?: () => void;
  onEditEnd?: () => void;
  onVerificationSuccess?: () => void;
  forceEditing?: boolean;
  firstName?: string;
  lastName?: string;
  isGuestConverted?: boolean;
}

export interface EmailFieldRef {
  startEditing: () => void;
}

export const EmailField = forwardRef<EmailFieldRef, EmailFieldProps>(
  (
    {
      initialValue,
      onSave,
      onEditStart,
      onEditEnd,
      onVerificationSuccess,
      forceEditing,
      firstName,
      lastName,
      isGuestConverted = false,
    },
    ref
  ) => {
    const { user } = useUser();
    const { isSignedIn } = useAuth();
    const clerk = useClerk();
    const { convertGuest, isConverting } = useGuestConversion();
    const [isEditing, setIsEditing] = useState(!!forceEditing);
    const [value, setValue] = useState(initialValue);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState('');

    useEffect(() => {
      if (forceEditing) setIsEditing(true);
    }, [forceEditing]);

    useEffect(() => {
      if (isGuestConverted && !isEditing) {
        setIsEditing(true);
      }
    }, [isGuestConverted, isEditing]);

    useEffect(() => {
      if (isSignedIn && initialValue !== value) {
        setValue(initialValue);
      }
    }, [initialValue, isSignedIn, value]);

    useImperativeHandle(ref, () => ({
      startEditing: () => {
        setValue(initialValue);
        setIsEditing(true);
        setError('');
        onEditStart?.();
      },
    }));

    const waitForAuthentication = useCallback(
      async (maxWaitTime = 5000): Promise<boolean> => {
        const startTime = Date.now();

        while (Date.now() - startTime < maxWaitTime) {
          const clerkAuthenticated = clerk.loaded && clerk.user && clerk.session;

          if (clerkAuthenticated) {
            return true;
          }

          await new Promise((resolve) => setTimeout(resolve, 200));
        }

        return false;
      },
      [clerk]
    );

    const handleSendVerificationCode = useCallback(
      async (email: string): Promise<boolean> => {
        console.log('EmailField handleSendVerificationCode called with:', email);
        const clerkAuthenticated = clerk.loaded && clerk.user && clerk.session;
        console.log('Clerk authenticated:', clerkAuthenticated);

        if (!clerkAuthenticated) {
          console.log('Not authenticated, returning true');
          return true;
        }

        if (!user || !clerk.user) {
          console.log('Waiting for authentication...');
          const authReady = await waitForAuthentication();

          if (!authReady) {
            console.log('Authentication timeout');
            setError('Authentication timeout. Please try again.');
            return false;
          }
        }

        const currentUser = user || clerk.user;
        if (!currentUser) {
          console.log('No current user found');
          setError('User authentication failed. Please try again.');
          return false;
        }

        try {
          console.log('Reloading user...');
          await currentUser.reload();

          let emailObj = currentUser.emailAddresses.find((e) => e.emailAddress === email);
          console.log('Found email object:', !!emailObj);

          if (!emailObj) {
            console.log('Creating new email address...');
            emailObj = await currentUser.createEmailAddress({ email });
          }

          console.log('Preparing verification...');
          await emailObj.prepareVerification({ strategy: 'email_code' });
          console.log('Verification prepared successfully');
          return true;
        } catch (err) {
          console.error('Error sending verification code:', err);
          setError(err instanceof Error ? err.message : 'Failed to send verification code');
          return false;
        }
      },
      [isSignedIn, user, clerk, waitForAuthentication]
    );

    const handleVerifyCode = useCallback(
      async (code: string, email: string): Promise<boolean> => {
        if (!isSignedIn) {
          setIsEditing(false);
          onEditEnd?.();
          onSave?.(email);
          onVerificationSuccess?.();
          return true;
        }

        if (!user) return false;

        try {
          const emailObj = user.emailAddresses.find((e) => e.emailAddress === email);
          if (!emailObj) throw new Error('Email address not found');

          await emailObj.attemptVerification({ code });

          if (!user.primaryEmailAddressId || user.primaryEmailAddressId !== emailObj.id) {
            await user.update({ primaryEmailAddressId: emailObj.id });
          }

          await user.reload();

          const isVerified = emailObj?.verification?.status === 'verified';

          if (isVerified) {
            setIsEditing(false);
            onEditEnd?.();
            onSave?.(emailObj.emailAddress);
            onVerificationSuccess?.();
          }

          return isVerified;
        } catch (err) {
          console.error('Error verifying code:', err);
          setError(err instanceof Error ? err.message : 'Failed to verify code');
          return false;
        }
      },
      [isSignedIn, user, value, onEditEnd, onSave, onVerificationSuccess]
    );

    const handleGuestConvertAndVerifyEmail = useCallback(
      async (email: string): Promise<boolean> => {
        if (!firstName || !lastName) {
          setError('Missing user information for conversion');
          return false;
        }

        try {
          const conversionSuccess = await convertGuest({
            email,
            firstName,
            lastName,
          });

          if (conversionSuccess) {
            setValue(email);
            return true;
          } else {
            setError('Failed to convert guest user');
            return false;
          }
        } catch (err) {
          console.error('Error during guest conversion:', err);
          const errorMessage = err instanceof Error ? err.message : 'Failed to convert guest user';
          setError(errorMessage);
          return false;
        }
      },
      [firstName, lastName, convertGuest]
    );

    const handleSave = useCallback(
      async (inputValue: string | AddressFields) => {
        const email = typeof inputValue === 'string' ? inputValue : value;

        if (!email.trim()) {
          setError('Please enter an email address');
          return;
        }

        setLoading(true);
        setError('');

        try {
          setValue(email);
          setIsEditing(false);
          onEditEnd?.();
          onSave?.(email);
        } catch (err) {
          const errorMessage = err instanceof Error ? err.message : 'Failed to save email address';
          setError(errorMessage);
        } finally {
          setLoading(false);
        }
      },
      [value, onEditEnd, onSave]
    );

    const handleChange = useCallback((newValue: string | AddressFields) => {
      if (typeof newValue === 'string') {
        setValue(newValue);
      }
    }, []);

    if (isEditing) {
      return (
        <div className={styles.editingContainer}>
          <MultiFieldInput
            type={MultiFieldInputType.EMAIL}
            value={value}
            onChange={handleChange}
            onSendEmailVerificationCode={handleSendVerificationCode}
            onVerifyEmailCode={handleVerifyCode}
            onSave={handleSave}
            loading={loading || isConverting}
            hideSaveButton={true}
            hideTitle={true}
            hideBorder={true}
            placeholder="Enter your email address"
            disabled={false}
            containerClassName={styles.noPaddingContainer}
            error={error}
            isGuestConverted={isGuestConverted}
            onGuestConvertAndVerifyEmail={handleGuestConvertAndVerifyEmail}
          />
        </div>
      );
    }

    return (
      <div className={styles.displayContainer} style={{ width: 'inherit' }}>
        <span className={styles.value}>{value || '—'}</span>
      </div>
    );
  }
);

EmailField.displayName = 'EmailField';
