import React, { useState, useEffect, useRef, ChangeEvent, useCallback, KeyboardEvent } from 'react';
import { Button } from '../Button';
import { Modal } from '../Modal';
import { ButtonColor, ButtonSize, ButtonState, ButtonType } from '../Button/Button.types';
import {
  MultiFieldInputProps,
  MultiFieldInputType,
  MultiFieldInputMode,
  AddressFields,
} from './MultiFieldInput.types';
import {
  DEFAULT_PLACEHOLDERS,
  DEFAULT_SAVE_BUTTON_TEXT,
  DEFAULT_TITLES,
  DEFAULT_MANUAL_ENTRY_TEXT,
  ADDRESS_MANUAL_TITLE,
  ADDRESS_FIELD_PLACEHOLDERS,
  ADDRESS_LOOKUP_TEXT,
  PHONE_VERIFICATION,
  EMAIL_VERIFICATION,
} from './MultiFieldInput.constants';
import { ArrowDown01Icon, ArrowUp01Icon } from '@hugeicons/react';
import classNames from 'classnames';
import { useWidgets } from '@/hooks/useWidgets';
import { useAuth, useClerk } from '@clerk/nextjs';
import styles from './MultiFieldInput.module.scss';
import { useBreakpoint } from '@/utils/breakpointUtils';

import { useVerificationModal } from '@/hooks/useVerificationModal';
import { useAddressAutocomplete, Option } from '@/hooks/useAddressAutocomplete';

const formatPhoneNumber = (input: string): string => {
  let formatted = input.replace(/[^\d+]/g, '');

  if (formatted.startsWith('+')) {
    formatted = '+' + formatted.substring(1).replace(/\+/g, '');
  }

  if (formatted.length > 0 && !formatted.startsWith('+')) {
    formatted = '+' + formatted;
  }

  return formatted;
};

export const MultiFieldInput: React.FC<MultiFieldInputProps> = ({
  type = MultiFieldInputType.PHONE,
  title,
  value: externalValue = type === MultiFieldInputType.ADDRESS
    ? { line1: '', line2: '', city: '', postcode: '' }
    : '',
  placeholder,
  onSave,
  saveButtonText,
  className,
  containerClassName,
  children,
  loading = false,
  disabled = false,
  options = [],
  manualEntryText,
  onSelectOption,
  onSwitchToManual,
  initialMode = type === MultiFieldInputType.ADDRESS
    ? MultiFieldInputMode.SELECT
    : MultiFieldInputMode.MANUAL,
  onSendVerificationCode,
  onVerifyCode,
  onSendEmailVerificationCode,
  onVerifyEmailCode,
  onGuestConvertAndVerifyEmail,
  hideBorder = false,
  hideSaveButton = false,
  onChange,
  forceDropdownPosition,
  hideTitle = false,
  compactMode = false,
  error,
  isGuestConverted = false,
}) => {
  const [value, setValue] = useState(externalValue);
  const [mode, setMode] = useState<MultiFieldInputMode>(initialMode);
  const [isOpen, setIsOpen] = useState(false);
  const [dropdownPosition, setDropdownPosition] = useState<'top' | 'bottom'>(
    forceDropdownPosition || 'bottom'
  );
  const [valueId, setValueId] = useState('');
  const { findAddresses } = useWidgets();
  const { getToken, isSignedIn } = useAuth();
  const { isMobile } = useBreakpoint();
  const clerk = useClerk();
  const dropdownRef = useRef<HTMLDivElement>(null);
  const dropdownContainerRef = useRef<HTMLDivElement>(null);
  const contentEditableDivRef = useRef<HTMLDivElement>(null);

  const { localOptions, setLocalOptions, debouncedFindAddresses } =
    useAddressAutocomplete<Option>(findAddresses);

  const phoneVerification = useVerificationModal({
    onSendCode: onSendVerificationCode ?? (async () => false),
    onVerifyCode: onVerifyCode ?? (async () => false),
    onSave,
    type: MultiFieldInputType.PHONE,
    loading,
    clerk,
  });

  const emailVerification = useVerificationModal({
    onSendCode: onSendEmailVerificationCode ?? (async () => false),
    onVerifyCode: onVerifyEmailCode ?? (async () => false),
    onSave,
    type: MultiFieldInputType.EMAIL,
    loading,
    clerk,
  });

  const showManualEntry =
    type === MultiFieldInputType.ADDRESS && mode === MultiFieldInputMode.SELECT;
  const isAddressManual =
    type === MultiFieldInputType.ADDRESS && mode === MultiFieldInputMode.MANUAL;

  const calculateDropdownPosition = () => {
    if (!dropdownContainerRef.current || !dropdownRef.current || forceDropdownPosition) {
      return;
    }

    const containerRect = dropdownContainerRef.current.getBoundingClientRect();
    const dropdownHeight = 400;
    const windowHeight = window.innerHeight;
    const spaceBelow = windowHeight - containerRect.bottom;

    const newPosition = spaceBelow < dropdownHeight ? 'top' : 'bottom';
    setDropdownPosition(newPosition);
  };

  useEffect(() => {
    if (forceDropdownPosition) {
      setDropdownPosition(forceDropdownPosition);
    }
  }, [forceDropdownPosition]);

  useEffect(() => {
    if (isOpen) {
      calculateDropdownPosition();
      window.addEventListener('resize', calculateDropdownPosition);
      window.addEventListener('scroll', calculateDropdownPosition);
    }

    return () => {
      window.removeEventListener('resize', calculateDropdownPosition);
      window.removeEventListener('scroll', calculateDropdownPosition);
    };
  }, [isOpen]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  useEffect(() => {
    if (contentEditableDivRef.current && typeof value === 'string') {
      if (value !== contentEditableDivRef.current.textContent) {
        contentEditableDivRef.current.textContent = value;
      }
    }
  }, [value]);

  const handleDivInput = async (e: React.FormEvent<HTMLDivElement>) => {
    const newValue = (e.target as HTMLDivElement).innerText;
    setValue(newValue);
    const token = await getToken();
    if (type === MultiFieldInputType.ADDRESS && token) {
      debouncedFindAddresses(newValue, token);
    }
    if (onChange) {
      onChange(newValue);
    }
    setIsOpen(true);
  };

  const handleInputChange = async (
    e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
    field?: keyof AddressFields
  ) => {
    if (type === MultiFieldInputType.ADDRESS && mode === MultiFieldInputMode.MANUAL && field) {
      const newValue = {
        ...(value as AddressFields),
        [field]: e.target.value,
      };
      if (isAddressManual) {
        setValue(newValue);
        if (onChange) {
          onChange(newValue);
        }
      }
    } else {
      let newValue = e.target.value;
      if (type === MultiFieldInputType.PHONE) {
        newValue = formatPhoneNumber(newValue);
      }
      setValue(newValue);
      if (type === MultiFieldInputType.ADDRESS) {
        const token = await getToken();
        if (token) {
          debouncedFindAddresses(newValue, token);
        }
      }
      if (onChange) {
        onChange(newValue);
      }
    }
    setIsOpen(true);
  };

  const handleSave = () => {
    if (
      typeof value === 'string'
        ? value.trim()
        : Object.values(value as AddressFields).some((v) => v.trim())
    ) {
      if (type === MultiFieldInputType.PHONE) {
        phoneVerification.handleSendCode(typeof value === 'string' ? value : '');
      } else if (type === MultiFieldInputType.EMAIL) {
        if (!isSignedIn && onGuestConvertAndVerifyEmail) {
          handleGuestConvertAndVerifyEmail();
        } else {
          emailVerification.handleSendCode(typeof value === 'string' ? value : '');
        }
      } else if (onSave) {
        if (type === MultiFieldInputType.ADDRESS) {
          if (!isAddressManual) {
            onSave(valueId);
          } else {
            onSave(value);
          }
        } else {
          onSave(value);
        }
      }
    }
  };

  const handleSelectOption = (option: Option) => {
    setValue(option.label);
    setValueId(option.value);
    onSelectOption?.(option);
    setIsOpen(false);
  };

  const handleSwitchToManual = () => {
    setMode(MultiFieldInputMode.MANUAL);
    setValue({ line1: '', line2: '', city: '', postcode: '' });

    if (contentEditableDivRef.current) {
      contentEditableDivRef.current.textContent = '';
    }

    onSwitchToManual?.();
    setIsOpen(false);
  };

  const handleSwitchToSelect = () => {
    const addressValue = value as AddressFields;
    const formattedAddress = [
      addressValue.line1,
      addressValue.line2,
      addressValue.city,
      addressValue.postcode,
    ]
      .filter(Boolean)
      .join(', ');

    setMode(MultiFieldInputMode.SELECT);
    setValue(formattedAddress);
  };

  const toggleDropdown = () => {
    if (!disabled && !loading) {
      setIsOpen(!isOpen);
    }
  };

  const defaultTitle =
    type === MultiFieldInputType.ADDRESS && mode === MultiFieldInputMode.MANUAL
      ? ADDRESS_MANUAL_TITLE
      : DEFAULT_TITLES[type as keyof typeof DEFAULT_TITLES];
  const defaultPlaceholder = DEFAULT_PLACEHOLDERS[type as keyof typeof DEFAULT_PLACEHOLDERS];
  const defaultButtonText = DEFAULT_SAVE_BUTTON_TEXT[type as keyof typeof DEFAULT_SAVE_BUTTON_TEXT];
  const defaultManualEntryText = DEFAULT_MANUAL_ENTRY_TEXT[MultiFieldInputType.ADDRESS];

  const hasValue = React.useMemo(() => {
    if (type === MultiFieldInputType.ADDRESS) {
      if (mode === MultiFieldInputMode.SELECT) {
        return typeof value === 'string' && value.trim() !== '';
      }
      const addressValue = value as AddressFields;
      return Object.values(addressValue).some((v) => v.trim() !== '');
    }
    return typeof value === 'string' && value.trim() !== '';
  }, [value, type, mode]);

  const renderSelectMode = () => (
    <>
      <div className={styles.selectContainer} ref={dropdownContainerRef}>
        <div
          contentEditable={true}
          ref={contentEditableDivRef}
          aria-label={placeholder || DEFAULT_PLACEHOLDERS[MultiFieldInputType.ADDRESS]}
          onInput={!disabled ? handleDivInput : undefined}
          data-placeholder={placeholder || DEFAULT_PLACEHOLDERS[MultiFieldInputType.ADDRESS]}
          className={classNames(styles.input, styles.selectInput, {
            [styles.disabled]: disabled,
            [styles.hasValue]: hasValue,
            [styles.empty]: !hasValue,
          })}
        />
        {localOptions.length > 0 ||
          (options.length > 0 && (
            <>
              {isOpen ? (
                <ArrowUp01Icon
                  size={16}
                  className={styles.iconInsideInput}
                  onClick={toggleDropdown}
                />
              ) : (
                <ArrowDown01Icon
                  size={16}
                  className={styles.iconInsideInput}
                  onClick={toggleDropdown}
                />
              )}
            </>
          ))}
        {isOpen && (
          <div
            ref={dropdownRef}
            className={classNames(styles.dropdown, {
              [styles.dropdownTop]: dropdownPosition === 'top',
              [styles.dropdownBottom]: dropdownPosition === 'bottom',
            })}
          >
            {localOptions.length > 0
              ? localOptions.map((option) => (
                  <div
                    key={option.value}
                    className={styles.option}
                    onClick={() => handleSelectOption(option)}
                  >
                    {option.label}
                  </div>
                ))
              : null}
          </div>
        )}
      </div>

      <div className={styles.actionsContainer}>
        {showManualEntry && (
          <div className={styles.manualEntry} onClick={handleSwitchToManual}>
            {manualEntryText || defaultManualEntryText}
          </div>
        )}

        <div className={styles.buttonContainer}>
          {!hideSaveButton && (
            <Button
              type={ButtonType.PRIMARY}
              color={ButtonColor.GREEN_PRIMARY}
              size={ButtonSize.BASE}
              state={disabled ? ButtonState.DISABLED : ButtonState.DEFAULT}
              onClick={handleSave}
              loading={loading}
            >
              {saveButtonText || defaultButtonText}
            </Button>
          )}
        </div>
      </div>
    </>
  );

  const renderAddressFields = () => {
    const addressValue = value as AddressFields;
    return (
      <div className={styles.addressFields}>
        <div className={styles.fieldGroup}>
          <label>Address Line 1</label>
          <input
            type="text"
            value={addressValue.line1}
            onChange={(e) => handleInputChange(e, 'line1')}
            placeholder={ADDRESS_FIELD_PLACEHOLDERS.line1}
            className={classNames(styles.input, {
              [styles.disabled]: disabled,
            })}
            disabled={disabled || loading}
          />
        </div>
        <div className={styles.fieldGroup}>
          <label>Address Line 2 (Optional)</label>
          <input
            type="text"
            value={addressValue.line2}
            onChange={(e) => handleInputChange(e, 'line2')}
            placeholder={ADDRESS_FIELD_PLACEHOLDERS.line2}
            className={classNames(styles.input, {
              [styles.disabled]: disabled,
            })}
            disabled={disabled || loading}
          />
        </div>
        <div className={styles.fieldGroup}>
          <label>City</label>
          <input
            type="text"
            value={addressValue.city}
            onChange={(e) => handleInputChange(e, 'city')}
            placeholder={ADDRESS_FIELD_PLACEHOLDERS.city}
            className={classNames(styles.input, {
              [styles.disabled]: disabled,
            })}
            disabled={disabled || loading}
          />
        </div>
        <div className={styles.fieldGroup}>
          <label>Postcode</label>
          <input
            type="text"
            value={addressValue.postcode}
            onChange={(e) => handleInputChange(e, 'postcode')}
            placeholder={ADDRESS_FIELD_PLACEHOLDERS.postcode}
            className={classNames(styles.input, {
              [styles.disabled]: disabled,
            })}
            disabled={disabled || loading}
          />
        </div>
        <div className={classNames(styles.actionsContainer, styles.manualActions)}>
          <div
            className={classNames(styles.manualEntry, styles.addressLookup)}
            onClick={handleSwitchToSelect}
          >
            {ADDRESS_LOOKUP_TEXT}
          </div>
          {!hideSaveButton && (
            <Button
              type={ButtonType.PRIMARY}
              color={ButtonColor.GREEN_PRIMARY}
              size={ButtonSize.BASE}
              state={disabled ? ButtonState.DISABLED : ButtonState.DEFAULT}
              onClick={handleSave}
              loading={loading}
            >
              {saveButtonText || defaultButtonText}
            </Button>
          )}
        </div>
      </div>
    );
  };

  const renderVerificationCodeInput = (verification: ReturnType<typeof useVerificationModal>) => (
    <div className={styles.codeFieldContainer}>
      <div className={styles.codeFieldInputs}>
        {[0, 1, 2, 3, 4, 5].map((index) => (
          <input
            key={`digit-${index}`}
            ref={(el: HTMLInputElement | null) => {
              (verification.codeInputRefs.current as (HTMLInputElement | null)[])[index] = el;
            }}
            className={styles.codeFieldInput}
            id={`digit-${index}-field`}
            autoComplete="one-time-code"
            aria-label={
              index === 0 ? `Enter verification code. Digit ${index + 1}` : `Digit ${index + 1}`
            }
            inputMode="numeric"
            name={`codeInput-${index}`}
            type="text"
            maxLength={1}
            value={verification.verificationDigits[index]}
            onChange={(e) => verification.handleDigitChange(index, e.target.value)}
            onKeyDown={(e) => verification.handleDigitKeyDown(index, e)}
            onPaste={index === 0 ? verification.handlePaste : undefined}
            onFocus={verification.handleDigitFocus}
            onInput={(e) => verification.handleDigitInput(e.target as HTMLInputElement)}
            onClick={(e) => (e.target as HTMLInputElement).select()}
          />
        ))}
      </div>
    </div>
  );

  const renderPhoneVerification = () => {
    return renderPhoneInput();
  };

  const renderEmailVerification = () => {
    return renderEmailInput();
  };

  const renderPhoneInput = () => {
    const inputValue = typeof value === 'string' ? value : '';

    return (
      <>
        <input
          type="tel"
          value={inputValue}
          onChange={(e) => {
            handleInputChange(e);
          }}
          placeholder={placeholder || DEFAULT_PLACEHOLDERS[MultiFieldInputType.PHONE]}
          className={classNames(styles.input, {
            [styles.disabled]: disabled,
          })}
          disabled={disabled || loading}
        />
        {error && <div className={styles.error}>{error}</div>}
        <div
          className={classNames(
            styles.buttonContainer,
            styles.actionsContainer,
            { [styles.buttonLeft]: true },
            { [styles.buttonWithMargin]: !error }
          )}
        >
          <Button
            type={ButtonType.PRIMARY}
            color={ButtonColor.GREEN_PRIMARY}
            size={ButtonSize.BASE}
            state={disabled || !hasValue ? ButtonState.DISABLED : ButtonState.DEFAULT}
            onClick={handleSave}
            loading={loading}
          >
            {type === MultiFieldInputType.PHONE
              ? PHONE_VERIFICATION.SEND_CODE_BUTTON
              : saveButtonText || defaultButtonText}
          </Button>
        </div>
      </>
    );
  };

  const renderEmailInput = () => {
    const inputValue = typeof value === 'string' ? value : '';

    if (!isSignedIn || isGuestConverted) {
      return (
        <>
          <input
            type="email"
            value={inputValue}
            onChange={(e) => {
              handleInputChange(e);
            }}
            placeholder={placeholder || DEFAULT_PLACEHOLDERS[MultiFieldInputType.EMAIL]}
            className={classNames(styles.input, {
              [styles.disabled]: disabled,
            })}
            disabled={disabled || loading}
          />
          {error && <div className={styles.error}>{error}</div>}
          <div
            className={classNames(
              styles.buttonContainer,
              styles.actionsContainer,
              { [styles.buttonLeft]: true },
              { [styles.buttonWithMargin]: !error }
            )}
          >
            <Button
              type={ButtonType.PRIMARY}
              color={ButtonColor.GREEN_PRIMARY}
              size={ButtonSize.BASE}
              state={disabled || !hasValue ? ButtonState.DISABLED : ButtonState.DEFAULT}
              onClick={handleSave}
              loading={loading}
            >
              {type === MultiFieldInputType.EMAIL
                ? EMAIL_VERIFICATION.SEND_CODE_BUTTON
                : saveButtonText || defaultButtonText}
            </Button>
          </div>
        </>
      );
    }

    return (
      <>
        <div className={styles.emailText}>{inputValue || '—'}</div>
        {error && <div className={styles.error}>{error}</div>}
        <div
          className={classNames(
            styles.buttonContainer,
            styles.actionsContainer,
            { [styles.buttonLeft]: true },
            { [styles.buttonWithMargin]: !error }
          )}
        >
          <Button
            type={ButtonType.PRIMARY}
            color={ButtonColor.GREEN_PRIMARY}
            size={ButtonSize.BASE}
            state={disabled || !hasValue ? ButtonState.DISABLED : ButtonState.DEFAULT}
            onClick={handleSave}
            loading={loading}
          >
            {type === MultiFieldInputType.EMAIL
              ? EMAIL_VERIFICATION.SEND_CODE_BUTTON
              : saveButtonText || defaultButtonText}
          </Button>
        </div>
      </>
    );
  };

  const renderManualMode = () => {
    if (isAddressManual) {
      return renderAddressFields();
    }

    const isPhone = type === MultiFieldInputType.PHONE;
    if (isPhone) {
      return renderPhoneVerification();
    }

    const isEmail = type === MultiFieldInputType.EMAIL;
    if (isEmail) {
      return renderEmailVerification();
    }

    const inputValue = typeof value === 'string' ? value : '';
    const isAccessInstruction = type === MultiFieldInputType.ACCESS_INSTRUCTION;

    return (
      <>
        {isAccessInstruction ? (
          <textarea
            value={inputValue}
            onChange={(e) => {
              handleInputChange(e);
            }}
            placeholder={placeholder || 'For example: Instructions about access, parking, or pets.'}
            className={classNames(styles.input, styles.accessInstructionTextarea, {
              [styles.disabled]: disabled,
            })}
            disabled={disabled || loading}
            rows={3}
            style={{ height: '96px', resize: 'none' }}
          />
        ) : (
          <>
            <input
              type={isPhone ? 'tel' : 'text'}
              value={inputValue}
              onChange={(e) => {
                handleInputChange(e);
              }}
              placeholder={placeholder || defaultPlaceholder}
              className={classNames(styles.input, {
                [styles.disabled]: disabled,
              })}
              disabled={disabled || loading}
            />
          </>
        )}
        <div
          className={classNames(styles.buttonContainer, styles.actionsContainer, {
            [styles.buttonLeft]: true,
          })}
        >
          {!hideSaveButton && (
            <Button
              type={ButtonType.PRIMARY}
              color={ButtonColor.GREEN_PRIMARY}
              size={ButtonSize.BASE}
              state={disabled ? ButtonState.DISABLED : ButtonState.DEFAULT}
              onClick={handleSave}
              loading={loading}
            >
              {saveButtonText || defaultButtonText}
            </Button>
          )}
        </div>
      </>
    );
  };

  const handleGuestConvertAndVerifyEmail = async () => {
    if (onGuestConvertAndVerifyEmail && typeof value === 'string') {
      await onGuestConvertAndVerifyEmail(value);
    }
  };

  return (
    <>
      <div
        className={classNames(styles.container, className, containerClassName, {
          [styles.mobile]: isMobile,
          [styles.noBorder]: hideBorder,
          [styles.compact]: compactMode,
        })}
      >
        {(title || defaultTitle) &&
          !hideTitle &&
          !(type === MultiFieldInputType.ADDRESS && mode === MultiFieldInputMode.MANUAL) && (
            <div
              className={classNames(styles.title, {
                [styles.addressTitle]:
                  type === MultiFieldInputType.ADDRESS && mode === MultiFieldInputMode.MANUAL,
              })}
            >
              {title || defaultTitle}
            </div>
          )}
        <div className={styles.inputContainer}>
          {mode === MultiFieldInputMode.SELECT ? renderSelectMode() : renderManualMode()}
        </div>
        {children}
      </div>

      {/* Phone Verification Modal */}
      <Modal
        open={phoneVerification.isModalOpen}
        onClose={phoneVerification.handleCloseModal}
        title={PHONE_VERIFICATION.VERIFICATION_TITLE}
        style={{ maxWidth: '400px', minWidth: 'inherit' }}
      >
        <div className={styles.verificationContainer}>
          <div className={styles.phoneVerificationInfo}>
            <p>
              <b>Enter the 6-digit code</b>
            </p>
            <p>Verification code sent to {phoneVerification.targetValue}</p>
          </div>
          {renderVerificationCodeInput(phoneVerification)}
          {phoneVerification.verificationError && (
            <div className={styles.verificationError}>{phoneVerification.verificationError}</div>
          )}
          <div className={styles.verificationActions}>
            <Button
              type={ButtonType.TERTIARY}
              color={ButtonColor.GREEN_PRIMARY}
              size={ButtonSize.L}
              state={
                phoneVerification.resendCountdown && phoneVerification.isResendDisabled
                  ? ButtonState.DISABLED
                  : ButtonState.DEFAULT
              }
              disabled={!!phoneVerification.resendCountdown && !!phoneVerification.isResendDisabled}
              onClick={() => phoneVerification.handleSendCode(phoneVerification.targetValue)}
            >
              {`${PHONE_VERIFICATION.RESEND_CODE}${phoneVerification.resendCountdown ? ` (${phoneVerification.resendCountdown})` : ''}`}
            </Button>
            <Button
              type={ButtonType.PRIMARY}
              color={ButtonColor.GREEN_PRIMARY}
              size={ButtonSize.L}
              state={
                phoneVerification.verificationCode.length !== 6
                  ? ButtonState.DISABLED
                  : ButtonState.DEFAULT
              }
              onClick={phoneVerification.handleVerifyCode}
              loading={loading}
            >
              {PHONE_VERIFICATION.VERIFY_BUTTON}
            </Button>
            <Button
              type={ButtonType.TERTIARY}
              color={ButtonColor.GREEN_PRIMARY}
              size={ButtonSize.L}
              state={ButtonState.DEFAULT}
              onClick={phoneVerification.handleCloseModal}
            >
              Edit phone number
            </Button>
          </div>
        </div>
      </Modal>

      {/* Email Verification Modal */}
      <Modal
        open={emailVerification.isModalOpen}
        onClose={emailVerification.handleCloseModal}
        title={EMAIL_VERIFICATION.VERIFICATION_TITLE}
        style={{ maxWidth: '400px', minWidth: 'inherit' }}
      >
        <div className={styles.verificationContainer}>
          <div className={styles.phoneVerificationInfo}>
            <p>
              <b>Enter the 6-digit code</b>
            </p>
            <p>Verification code sent to {emailVerification.targetValue}</p>
          </div>
          {renderVerificationCodeInput(emailVerification)}
          {emailVerification.verificationError && (
            <div className={styles.verificationError}>{emailVerification.verificationError}</div>
          )}
          <div className={styles.verificationActions}>
            <Button
              type={ButtonType.TERTIARY}
              color={ButtonColor.GREEN_PRIMARY}
              size={ButtonSize.L}
              state={
                emailVerification.resendCountdown && emailVerification.isResendDisabled
                  ? ButtonState.DISABLED
                  : ButtonState.DEFAULT
              }
              disabled={!!emailVerification.resendCountdown && !!emailVerification.isResendDisabled}
              onClick={() => emailVerification.handleSendCode(emailVerification.targetValue)}
            >
              {`${EMAIL_VERIFICATION.RESEND_CODE}${emailVerification.resendCountdown ? ` (${emailVerification.resendCountdown})` : ''}`}
            </Button>
            <Button
              type={ButtonType.PRIMARY}
              color={ButtonColor.GREEN_PRIMARY}
              size={ButtonSize.L}
              state={
                emailVerification.verificationCode.length !== 6
                  ? ButtonState.DISABLED
                  : ButtonState.DEFAULT
              }
              onClick={emailVerification.handleVerifyCode}
              loading={loading}
            >
              {EMAIL_VERIFICATION.VERIFY_BUTTON}
            </Button>
            <Button
              type={ButtonType.TERTIARY}
              color={ButtonColor.GREEN_PRIMARY}
              size={ButtonSize.L}
              state={ButtonState.DEFAULT}
              onClick={() => {
                clerk.openUserProfile();
              }}
            >
              Edit email address
            </Button>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default MultiFieldInput;
