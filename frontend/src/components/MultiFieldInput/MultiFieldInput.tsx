import React, { useState, useEffect, useRef, ChangeEvent, useCallback, KeyboardEvent } from 'react';
import { Button } from '../Button';
import { Modal } from '../Modal';
import { ButtonColor, ButtonSize, ButtonState, ButtonType } from '../Button/Button.types';
import {
  MultiFieldInputProps,
  MultiFieldInputType,
  MultiFieldInputMode,
  Option,
  AddressFields,
  AddressSearchResult,
  PhoneVerificationState,
} from './MultiFieldInput.types';
import {
  DEFAULT_PLACEHOLDERS,
  DEFAULT_SAVE_BUTTON_TEXT,
  DEFAULT_TITLES,
  DEFAULT_MANUAL_ENTRY_TEXT,
  ADDRESS_MANUAL_TITLE,
  ADDRESS_FIELD_PLACEHOLDERS,
  ADDRESS_LOOKUP_TEXT,
  PHONE_VERIFICATION,
  EMAIL_VERIFICATION,
} from './MultiFieldInput.constants';
import { ArrowDown01Icon, ArrowUp01Icon } from '@hugeicons/react';
import classNames from 'classnames';
import { useWidgets } from '@/hooks/useWidgets';
import { useAuth, useClerk } from '@clerk/nextjs';
import styles from './MultiFieldInput.module.scss';
import { useBreakpoint } from '@/utils/breakpointUtils';
import { debounce } from 'lodash';

const formatPhoneNumber = (input: string): string => {
  let formatted = input.replace(/[^\d+]/g, '');

  if (formatted.startsWith('+')) {
    formatted = '+' + formatted.substring(1).replace(/\+/g, '');
  }

  if (formatted.length > 0 && !formatted.startsWith('+')) {
    formatted = '+' + formatted;
  }

  return formatted;
};

export const MultiFieldInput: React.FC<MultiFieldInputProps> = ({
  type = MultiFieldInputType.PHONE,
  title,
  value: externalValue = type === MultiFieldInputType.ADDRESS
    ? { line1: '', line2: '', city: '', postcode: '' }
    : '',
  placeholder,
  onSave,
  saveButtonText,
  className,
  containerClassName,
  children,
  loading = false,
  disabled = false,
  options = [],
  manualEntryText,
  onSelectOption,
  onSwitchToManual,
  initialMode = type === MultiFieldInputType.ADDRESS
    ? MultiFieldInputMode.SELECT
    : MultiFieldInputMode.MANUAL,
  onSendVerificationCode,
  onVerifyCode,
  onSendEmailVerificationCode,
  onVerifyEmailCode,
  onGuestConvertAndVerifyEmail,
  hideBorder = false,
  hideSaveButton = false,
  onChange,
  forceDropdownPosition,
  hideTitle = false,
  compactMode = false,
  error,
  isGuestConverted = false,
}) => {
  const [value, setValue] = useState(externalValue);
  const [mode, setMode] = useState<MultiFieldInputMode>(initialMode);
  const [isOpen, setIsOpen] = useState(false);
  const [dropdownPosition, setDropdownPosition] = useState<'top' | 'bottom'>(
    forceDropdownPosition || 'bottom'
  );
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [verificationState, setVerificationState] = useState<PhoneVerificationState>(
    PhoneVerificationState.INITIAL
  );
  const [verificationCode, setVerificationCode] = useState('');
  const [verificationDigits, setVerificationDigits] = useState(['', '', '', '', '', '']);
  const [phoneNumber, setPhoneNumber] = useState('');
  const [verificationError, setVerificationError] = useState('');
  const [resendCountdown, setResendCountdown] = useState(0);
  const [isResendDisabled, setIsResendDisabled] = useState(false);
  const [isVerificationModalOpen, setIsVerificationModalOpen] = useState(false);
  const [emailAddress, setEmailAddress] = useState('');
  const [isEmailVerificationModalOpen, setIsEmailVerificationModalOpen] = useState(false);

  const clerk = useClerk();
  const dropdownRef = useRef<HTMLDivElement>(null);
  const dropdownContainerRef = useRef<HTMLDivElement>(null);
  const contentEditableDivRef = useRef<HTMLDivElement>(null);
  const [localOptions, setLocalOptions] = useState<Option[]>(options);
  const [valueId, setValueId] = useState('');
  const { findAddresses } = useWidgets();
  const { getToken, isSignedIn } = useAuth();
  const { isMobile } = useBreakpoint();

  const codeInputRefs = useRef<(HTMLInputElement | null)[]>([]);

  const showManualEntry =
    type === MultiFieldInputType.ADDRESS && mode === MultiFieldInputMode.SELECT;
  const isAddressManual =
    type === MultiFieldInputType.ADDRESS && mode === MultiFieldInputMode.MANUAL;

  const calculateDropdownPosition = () => {
    if (!dropdownContainerRef.current || !dropdownRef.current || forceDropdownPosition) {
      return;
    }

    const containerRect = dropdownContainerRef.current.getBoundingClientRect();
    const dropdownHeight = 400;
    const windowHeight = window.innerHeight;
    const spaceBelow = windowHeight - containerRect.bottom;

    const newPosition = spaceBelow < dropdownHeight ? 'top' : 'bottom';
    setDropdownPosition(newPosition);
  };

  useEffect(() => {
    if (forceDropdownPosition) {
      setDropdownPosition(forceDropdownPosition);
    }
  }, [forceDropdownPosition]);

  useEffect(() => {
    if (isOpen) {
      calculateDropdownPosition();
      window.addEventListener('resize', calculateDropdownPosition);
      window.addEventListener('scroll', calculateDropdownPosition);
    }

    return () => {
      window.removeEventListener('resize', calculateDropdownPosition);
      window.removeEventListener('scroll', calculateDropdownPosition);
    };
  }, [isOpen]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  useEffect(() => {
    if (contentEditableDivRef.current && typeof value === 'string') {
      if (value !== contentEditableDivRef.current.textContent) {
        contentEditableDivRef.current.textContent = value;
      }
    }
  }, [value]);

  useEffect(() => {
    let timer: NodeJS.Timeout;

    if (resendCountdown > 0) {
      timer = setTimeout(() => {
        setResendCountdown((prevCount) => prevCount - 1);
      }, 1000);
    } else if (resendCountdown === 0 && isResendDisabled) {
      setIsResendDisabled(false);
    }

    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [resendCountdown, isResendDisabled]);

  useEffect(() => {
    setVerificationCode(verificationDigits.join(''));
  }, [verificationDigits]);

  const debouncedFindAddresses = useCallback(
    debounce(async (query: string, token: string) => {
      try {
        const result = await findAddresses(query, token);
        const addressResult = result as AddressSearchResult;
        if (
          addressResult &&
          typeof addressResult === 'object' &&
          'hits' in addressResult &&
          Array.isArray(addressResult.hits)
        ) {
          const newOptions = addressResult.hits.map((item) => ({
            value: item.id || item.value || '',
            label: item.suggestion || item.label || '',
          }));
          setLocalOptions(newOptions);
        }
      } catch (error) {
        console.error('Error during autocomplete:', error);
      }
    }, 1000),
    [findAddresses]
  );

  const handleDivInput = async (e: React.FormEvent<HTMLDivElement>) => {
    const newValue = (e.target as HTMLDivElement).innerText;
    setValue(newValue);
    const token = await getToken();

    if (type === MultiFieldInputType.ADDRESS && token) {
      debouncedFindAddresses(newValue, token);
    }

    if (onChange) {
      onChange(newValue);
    }

    setIsOpen(true);
  };

  const handleInputChange = async (
    e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
    field?: keyof AddressFields
  ) => {
    if (type === MultiFieldInputType.ADDRESS && mode === MultiFieldInputMode.MANUAL && field) {
      const newValue = {
        ...(value as AddressFields),
        [field]: e.target.value,
      };

      if (isAddressManual) {
        setValue(newValue);
        if (onChange) {
          onChange(newValue);
        }
      }
    } else {
      let newValue = e.target.value;

      if (type === MultiFieldInputType.PHONE) {
        newValue = formatPhoneNumber(newValue);
      }

      setValue(newValue);

      if (type === MultiFieldInputType.ADDRESS) {
        const token = await getToken();
        if (token) {
          try {
            const result = await findAddresses(newValue, token);
            const addressResult = result as AddressSearchResult;
            if (
              addressResult &&
              typeof addressResult === 'object' &&
              'hits' in addressResult &&
              Array.isArray(addressResult.hits)
            ) {
              const newOptions = addressResult.hits.map((item) => ({
                value: item.id || item.value || '',
                label: item.suggestion || item.label || '',
              }));

              setLocalOptions(newOptions);
            }
          } catch (error) {
            console.error('Error during autocomplete:', error);
          }
        }
      }
      if (onChange) {
        onChange(newValue);
      }
    }
    setIsOpen(true);
  };

  const handleSendVerificationCode = async () => {
    if (onSendVerificationCode && typeof value === 'string') {
      setVerificationError('');
      setPhoneNumber(value);

      try {
        const result = await onSendVerificationCode(value);
        if (result) {
          setVerificationState(PhoneVerificationState.CODE_SENT);
          setIsVerificationModalOpen(true);

          // Start countdown for resend button
          setIsResendDisabled(true);
          setResendCountdown(30);
        } else {
          setVerificationError('Failed to send verification code');
        }
      } catch (error) {
        console.error('Error sending verification code:', error);
        setVerificationError('Error sending verification code');
      }
    }
  };

  const handleVerifyCode = async () => {
    if (onVerifyCode && phoneNumber && verificationCode) {
      setVerificationError('');

      try {
        const result = await onVerifyCode(verificationCode, phoneNumber);
        if (result) {
          setVerificationState(PhoneVerificationState.VERIFIED);
          setIsVerificationModalOpen(false);
          if (onSave) {
            onSave(phoneNumber);
          }
        } else {
          setVerificationError('Invalid verification code');
        }
      } catch (error) {
        console.error('Error verifying code:', error);
        setVerificationError('Error verifying code');
      }
    } else {
      setVerificationError('Please enter the verification code');
    }
  };

  const handleCloseVerificationModal = () => {
    setIsVerificationModalOpen(false);
    setVerificationState(PhoneVerificationState.INITIAL);
    setVerificationDigits(['', '', '', '', '', '']);
    setVerificationCode('');
    setVerificationError('');
  };

  // Helper function to wait for authentication state to be ready
  const waitForAuthState = async (maxWaitTime = 5000): Promise<boolean> => {
    const startTime = Date.now();

    while (Date.now() - startTime < maxWaitTime) {
      // Check clerk object directly for more reliable state
      const clerkReady = clerk.loaded && clerk.user && clerk.session;
      console.log('🔍 Checking clerk state:', {
        loaded: clerk.loaded,
        hasUser: !!clerk.user,
        hasSession: !!clerk.session,
        userId: clerk.user?.id,
      });

      if (clerkReady) {
        console.log('✅ Clerk state is ready!');
        return true;
      }

      // Wait 200ms before checking again
      await new Promise((resolve) => setTimeout(resolve, 200));
    }

    console.log('⏰ Timeout waiting for clerk state');
    return false;
  };

  const handleGuestConvertAndVerifyEmail = async () => {
    if (onGuestConvertAndVerifyEmail && typeof value === 'string') {
      setVerificationError('');
      setEmailAddress(value);

      console.log('🔄 Starting guest conversion for:', value);

      try {
        const result = await onGuestConvertAndVerifyEmail(value);
        if (result) {
          console.log('✅ Guest conversion successful, waiting for auth state...');

          // Wait for authentication state to be ready
          const authReady = await waitForAuthState();

          if (authReady) {
            console.log('📧 Auth state ready, triggering email verification');
            handleSendEmailVerificationCode();
          } else {
            console.log('❌ Auth state not ready, verification failed');
            setVerificationError('Authentication state not ready. Please try again.');
          }
        } else {
          console.log('❌ Guest conversion failed');
          setVerificationError('Failed to convert guest user');
        }
      } catch (error) {
        console.error('💥 Error during guest conversion:', error);
        setVerificationError('Error converting guest user');
      }
    }
  };

  const handleSendEmailVerificationCode = async () => {
    if (onSendEmailVerificationCode && typeof value === 'string') {
      setVerificationError('');
      setEmailAddress(value);

      try {
        const result = await onSendEmailVerificationCode(value);
        if (result) {
          setVerificationState(PhoneVerificationState.CODE_SENT);
          setIsEmailVerificationModalOpen(true);

          // Start countdown for resend button
          setIsResendDisabled(true);
          setResendCountdown(30);
        } else {
          setVerificationError('Failed to send verification code');
        }
      } catch (error) {
        console.error('Error sending email verification code:', error);
        setVerificationError('Error sending verification code');
      }
    }
  };

  const handleVerifyEmailCode = async () => {
    if (onVerifyEmailCode && emailAddress && verificationCode) {
      setVerificationError('');

      try {
        const result = await onVerifyEmailCode(verificationCode, emailAddress);
        if (result) {
          setVerificationState(PhoneVerificationState.VERIFIED);
          setIsEmailVerificationModalOpen(false);
          if (onSave) {
            onSave(emailAddress);
          }
        } else {
          setVerificationError('Invalid verification code');
        }
      } catch (error) {
        console.error('Error verifying email code:', error);
        setVerificationError('Error verifying code');
      }
    } else {
      setVerificationError('Please enter the verification code');
    }
  };

  // Remove the auto-trigger logic since we handle the sequence directly in handleSave
  // The conversion and verification flow is now coordinated in handleGuestConvertAndVerifyEmail

  const handleCloseEmailVerificationModal = () => {
    setIsEmailVerificationModalOpen(false);
    setVerificationState(PhoneVerificationState.INITIAL);
    setVerificationDigits(['', '', '', '', '', '']);
    setVerificationCode('');
    setVerificationError('');
  };

  const handleDigitChange = (index: number, value: string) => {
    // Only allow numbers
    const digit = value.replace(/\D/g, '').substring(0, 1);

    const newDigits = [...verificationDigits];
    newDigits[index] = digit;
    setVerificationDigits(newDigits);

    // Auto-focus next input if current input is filled
    if (digit && index < 5) {
      codeInputRefs.current[index + 1]?.focus();
    }
  };

  const handleDigitKeyDown = (index: number, e: KeyboardEvent<HTMLInputElement>) => {
    const target = e.target as HTMLInputElement;

    if (/^\d$/.test(e.key)) {
      e.preventDefault(); // Prevent default to handle the input manually

      if (e.key === verificationDigits[index] && index < 5) {
        codeInputRefs.current[index + 1]?.focus();
        return;
      }

      handleDigitChange(index, e.key);
      return;
    }

    // Handle backspace - move to previous input if current is empty
    if (e.key === 'Backspace' && !verificationDigits[index] && index > 0) {
      codeInputRefs.current[index - 1]?.focus();
      return;
    }

    // Handle left arrow - move to previous input
    if (e.key === 'ArrowLeft' && index > 0) {
      const prevInput = codeInputRefs.current[index - 1];
      prevInput?.focus();
      handleDigitInput(prevInput);
      return;
    }

    // Handle right arrow - move to next input
    if (e.key === 'ArrowRight' && index < 5) {
      const nextInput = codeInputRefs.current[index + 1];
      nextInput?.focus();
      handleDigitInput(nextInput);
      return;
    }

    // For any other key press, ensure the input is selected
    handleDigitInput(target);
  };

  const handleDigitFocus = (e: React.FocusEvent<HTMLInputElement>) => {
    // Select all text when input is focused
    e.target.select();
  };

  const handleDigitInput = (target: HTMLInputElement | null) => {
    if (!target) return;

    // Ensure cursor is at the end
    const length = target.value.length;

    // The setTimeout ensures this runs after the default browser behavior
    setTimeout(() => {
      try {
        if (target.setSelectionRange) {
          target.setSelectionRange(length, length);
        }
      } catch (err) {
        console.error('Error setting selection range:', err);
      }
    }, 0);
  };

  const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>) => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text');
    const digits = pastedData.replace(/\D/g, '').substring(0, 6).split('');

    const newDigits = [...verificationDigits];
    digits.forEach((digit, index) => {
      if (index < 6) {
        newDigits[index] = digit;
      }
    });

    setVerificationDigits(newDigits);

    // Focus the next empty input or the last one if all filled
    const nextEmptyIndex = newDigits.findIndex((d) => !d);
    if (nextEmptyIndex >= 0) {
      codeInputRefs.current[nextEmptyIndex]?.focus();
    } else if (newDigits[5]) {
      codeInputRefs.current[5]?.focus();
    }
  };

  const handleSave = () => {
    if (
      typeof value === 'string'
        ? value.trim()
        : Object.values(value as AddressFields).some((v) => v.trim())
    ) {
      if (type === MultiFieldInputType.PHONE) {
        handleSendVerificationCode();
      } else if (type === MultiFieldInputType.EMAIL) {
        // For guests, convert to authenticated user first, then verify email
        if (!isSignedIn && onGuestConvertAndVerifyEmail) {
          handleGuestConvertAndVerifyEmail();
        } else {
          // For authenticated users, use email verification flow directly
          handleSendEmailVerificationCode();
        }
      } else if (onSave) {
        if (type === MultiFieldInputType.ADDRESS) {
          if (!isAddressManual) {
            onSave(valueId);
          } else {
            onSave(value);
          }
        } else {
          onSave(value);
        }
      }
    }
  };

  const handleSelectOption = (option: Option) => {
    setValue(option.label);
    setValueId(option.value);
    onSelectOption?.(option);
    setIsOpen(false);
  };

  const handleSwitchToManual = () => {
    setMode(MultiFieldInputMode.MANUAL);
    setValue({ line1: '', line2: '', city: '', postcode: '' });

    if (contentEditableDivRef.current) {
      contentEditableDivRef.current.textContent = '';
    }

    onSwitchToManual?.();
    setIsOpen(false);
  };

  const handleSwitchToSelect = () => {
    const addressValue = value as AddressFields;
    const formattedAddress = [
      addressValue.line1,
      addressValue.line2,
      addressValue.city,
      addressValue.postcode,
    ]
      .filter(Boolean)
      .join(', ');

    setMode(MultiFieldInputMode.SELECT);
    setValue(formattedAddress);
  };

  const toggleDropdown = () => {
    if (!disabled && !loading) {
      setIsOpen(!isOpen);
    }
  };

  const defaultTitle =
    type === MultiFieldInputType.ADDRESS && mode === MultiFieldInputMode.MANUAL
      ? ADDRESS_MANUAL_TITLE
      : DEFAULT_TITLES[type as keyof typeof DEFAULT_TITLES];
  const defaultPlaceholder = DEFAULT_PLACEHOLDERS[type as keyof typeof DEFAULT_PLACEHOLDERS];
  const defaultButtonText = DEFAULT_SAVE_BUTTON_TEXT[type as keyof typeof DEFAULT_SAVE_BUTTON_TEXT];
  const defaultManualEntryText = DEFAULT_MANUAL_ENTRY_TEXT[MultiFieldInputType.ADDRESS];

  const hasValue = React.useMemo(() => {
    if (type === MultiFieldInputType.ADDRESS) {
      if (mode === MultiFieldInputMode.SELECT) {
        return typeof value === 'string' && value.trim() !== '';
      }
      const addressValue = value as AddressFields;
      return Object.values(addressValue).some((v) => v.trim() !== '');
    }
    return typeof value === 'string' && value.trim() !== '';
  }, [value, type, mode]);

  const renderSelectMode = () => (
    <>
      <div className={styles.selectContainer} ref={dropdownContainerRef}>
        <div
          contentEditable={true}
          ref={contentEditableDivRef}
          aria-label={placeholder || DEFAULT_PLACEHOLDERS[MultiFieldInputType.ADDRESS]}
          onInput={!disabled ? handleDivInput : undefined}
          data-placeholder={placeholder || DEFAULT_PLACEHOLDERS[MultiFieldInputType.ADDRESS]}
          className={classNames(styles.input, styles.selectInput, {
            [styles.disabled]: disabled,
            [styles.hasValue]: hasValue,
            [styles.empty]: !hasValue,
          })}
        />
        {localOptions.length > 0 ||
          (options.length > 0 && (
            <>
              {isOpen ? (
                <ArrowUp01Icon
                  size={16}
                  className={styles.iconInsideInput}
                  onClick={toggleDropdown}
                />
              ) : (
                <ArrowDown01Icon
                  size={16}
                  className={styles.iconInsideInput}
                  onClick={toggleDropdown}
                />
              )}
            </>
          ))}
        {isOpen && (
          <div
            ref={dropdownRef}
            className={classNames(styles.dropdown, {
              [styles.dropdownTop]: dropdownPosition === 'top',
              [styles.dropdownBottom]: dropdownPosition === 'bottom',
            })}
          >
            {localOptions.length > 0
              ? localOptions.map((option) => (
                  <div
                    key={option.value}
                    className={styles.option}
                    onClick={() => handleSelectOption(option)}
                  >
                    {option.label}
                  </div>
                ))
              : null}
          </div>
        )}
      </div>

      <div className={styles.actionsContainer}>
        {showManualEntry && (
          <div className={styles.manualEntry} onClick={handleSwitchToManual}>
            {manualEntryText || defaultManualEntryText}
          </div>
        )}

        <div className={styles.buttonContainer}>
          {!hideSaveButton && (
            <Button
              type={ButtonType.PRIMARY}
              color={ButtonColor.GREEN_PRIMARY}
              size={ButtonSize.BASE}
              state={disabled ? ButtonState.DISABLED : ButtonState.DEFAULT}
              onClick={handleSave}
              loading={loading}
            >
              {saveButtonText || defaultButtonText}
            </Button>
          )}
        </div>
      </div>
    </>
  );

  const renderAddressFields = () => {
    const addressValue = value as AddressFields;
    return (
      <div className={styles.addressFields}>
        <div className={styles.fieldGroup}>
          <label>Address Line 1</label>
          <input
            type="text"
            value={addressValue.line1}
            onChange={(e) => handleInputChange(e, 'line1')}
            placeholder={ADDRESS_FIELD_PLACEHOLDERS.line1}
            className={classNames(styles.input, {
              [styles.disabled]: disabled,
            })}
            disabled={disabled || loading}
          />
        </div>
        <div className={styles.fieldGroup}>
          <label>Address Line 2 (Optional)</label>
          <input
            type="text"
            value={addressValue.line2}
            onChange={(e) => handleInputChange(e, 'line2')}
            placeholder={ADDRESS_FIELD_PLACEHOLDERS.line2}
            className={classNames(styles.input, {
              [styles.disabled]: disabled,
            })}
            disabled={disabled || loading}
          />
        </div>
        <div className={styles.fieldGroup}>
          <label>City</label>
          <input
            type="text"
            value={addressValue.city}
            onChange={(e) => handleInputChange(e, 'city')}
            placeholder={ADDRESS_FIELD_PLACEHOLDERS.city}
            className={classNames(styles.input, {
              [styles.disabled]: disabled,
            })}
            disabled={disabled || loading}
          />
        </div>
        <div className={styles.fieldGroup}>
          <label>Postcode</label>
          <input
            type="text"
            value={addressValue.postcode}
            onChange={(e) => handleInputChange(e, 'postcode')}
            placeholder={ADDRESS_FIELD_PLACEHOLDERS.postcode}
            className={classNames(styles.input, {
              [styles.disabled]: disabled,
            })}
            disabled={disabled || loading}
          />
        </div>
        <div className={classNames(styles.actionsContainer, styles.manualActions)}>
          <div
            className={classNames(styles.manualEntry, styles.addressLookup)}
            onClick={handleSwitchToSelect}
          >
            {ADDRESS_LOOKUP_TEXT}
          </div>
          {!hideSaveButton && (
            <Button
              type={ButtonType.PRIMARY}
              color={ButtonColor.GREEN_PRIMARY}
              size={ButtonSize.BASE}
              state={disabled ? ButtonState.DISABLED : ButtonState.DEFAULT}
              onClick={handleSave}
              loading={loading}
            >
              {saveButtonText || defaultButtonText}
            </Button>
          )}
        </div>
      </div>
    );
  };

  const renderVerificationCodeInput = () => (
    <div className={styles.codeFieldContainer}>
      <div className={styles.codeFieldInputs}>
        {[0, 1, 2, 3, 4, 5].map((index) => (
          <input
            key={`digit-${index}`}
            ref={(el) => {
              codeInputRefs.current[index] = el;
            }}
            className={styles.codeFieldInput}
            id={`digit-${index}-field`}
            autoComplete="one-time-code"
            aria-label={
              index === 0 ? `Enter verification code. Digit ${index + 1}` : `Digit ${index + 1}`
            }
            inputMode="numeric"
            name={`codeInput-${index}`}
            type="text"
            maxLength={1}
            value={verificationDigits[index]}
            onChange={(e) => handleDigitChange(index, e.target.value)}
            onKeyDown={(e) => handleDigitKeyDown(index, e)}
            onPaste={index === 0 ? handlePaste : undefined}
            onFocus={handleDigitFocus}
            onInput={(e) => handleDigitInput(e.target as HTMLInputElement)}
            onClick={(e) => (e.target as HTMLInputElement).select()}
          />
        ))}
      </div>
    </div>
  );

  const renderPhoneVerification = () => {
    // Always show the phone input - verification happens in modal
    return renderPhoneInput();
  };

  const renderEmailVerification = () => {
    // Always show the email input - verification happens in modal
    return renderEmailInput();
  };

  const renderPhoneInput = () => {
    const inputValue = typeof value === 'string' ? value : '';

    return (
      <>
        <input
          type="tel"
          value={inputValue}
          onChange={(e) => {
            handleInputChange(e);
          }}
          placeholder={placeholder || DEFAULT_PLACEHOLDERS[MultiFieldInputType.PHONE]}
          className={classNames(styles.input, {
            [styles.disabled]: disabled,
          })}
          disabled={disabled || loading}
        />
        {error && <div className={styles.error}>{error}</div>}
        <div
          className={classNames(
            styles.buttonContainer,
            styles.actionsContainer,
            { [styles.buttonLeft]: true },
            { [styles.buttonWithMargin]: !error }
          )}
        >
          <Button
            type={ButtonType.PRIMARY}
            color={ButtonColor.GREEN_PRIMARY}
            size={ButtonSize.BASE}
            state={disabled || !hasValue ? ButtonState.DISABLED : ButtonState.DEFAULT}
            onClick={handleSave}
            loading={loading}
          >
            {type === MultiFieldInputType.PHONE
              ? PHONE_VERIFICATION.SEND_CODE_BUTTON
              : saveButtonText || defaultButtonText}
          </Button>
        </div>
      </>
    );
  };

  const renderEmailInput = () => {
    const inputValue = typeof value === 'string' ? value : '';

    if (!isSignedIn || isGuestConverted) {
      return (
        <>
          <input
            type="email"
            value={inputValue}
            onChange={(e) => {
              handleInputChange(e);
            }}
            placeholder={placeholder || DEFAULT_PLACEHOLDERS[MultiFieldInputType.EMAIL]}
            className={classNames(styles.input, {
              [styles.disabled]: disabled,
            })}
            disabled={disabled || loading}
          />
          {error && <div className={styles.error}>{error}</div>}
          <div
            className={classNames(
              styles.buttonContainer,
              styles.actionsContainer,
              { [styles.buttonLeft]: true },
              { [styles.buttonWithMargin]: !error }
            )}
          >
            <Button
              type={ButtonType.PRIMARY}
              color={ButtonColor.GREEN_PRIMARY}
              size={ButtonSize.BASE}
              state={disabled || !hasValue ? ButtonState.DISABLED : ButtonState.DEFAULT}
              onClick={handleSave}
              loading={loading}
            >
              {type === MultiFieldInputType.EMAIL
                ? EMAIL_VERIFICATION.SEND_CODE_BUTTON
                : saveButtonText || defaultButtonText}
            </Button>
          </div>
        </>
      );
    }

    return (
      <>
        <div className={styles.emailText}>{inputValue || '—'}</div>
        {error && <div className={styles.error}>{error}</div>}
        <div
          className={classNames(
            styles.buttonContainer,
            styles.actionsContainer,
            { [styles.buttonLeft]: true },
            { [styles.buttonWithMargin]: !error }
          )}
        >
          <Button
            type={ButtonType.PRIMARY}
            color={ButtonColor.GREEN_PRIMARY}
            size={ButtonSize.BASE}
            state={disabled || !hasValue ? ButtonState.DISABLED : ButtonState.DEFAULT}
            onClick={handleSave}
            loading={loading}
          >
            {type === MultiFieldInputType.EMAIL
              ? EMAIL_VERIFICATION.SEND_CODE_BUTTON
              : saveButtonText || defaultButtonText}
          </Button>
        </div>
      </>
    );
  };

  const renderManualMode = () => {
    if (isAddressManual) {
      return renderAddressFields();
    }

    const isPhone = type === MultiFieldInputType.PHONE;
    if (isPhone) {
      return renderPhoneVerification();
    }

    const isEmail = type === MultiFieldInputType.EMAIL;
    if (isEmail) {
      return renderEmailVerification();
    }

    const inputValue = typeof value === 'string' ? value : '';
    const isAccessInstruction = type === MultiFieldInputType.ACCESS_INSTRUCTION;

    return (
      <>
        {isAccessInstruction ? (
          <textarea
            value={inputValue}
            onChange={(e) => {
              handleInputChange(e);
            }}
            placeholder={placeholder || 'For example: Instructions about access, parking, or pets.'}
            className={classNames(styles.input, styles.accessInstructionTextarea, {
              [styles.disabled]: disabled,
            })}
            disabled={disabled || loading}
            rows={3}
            style={{ height: '96px', resize: 'none' }}
          />
        ) : (
          <>
            <input
              type={isPhone ? 'tel' : 'text'}
              value={inputValue}
              onChange={(e) => {
                handleInputChange(e);
              }}
              placeholder={placeholder || defaultPlaceholder}
              className={classNames(styles.input, {
                [styles.disabled]: disabled,
              })}
              disabled={disabled || loading}
            />
          </>
        )}
        <div
          className={classNames(styles.buttonContainer, styles.actionsContainer, {
            [styles.buttonLeft]: true,
          })}
        >
          {!hideSaveButton && (
            <Button
              type={ButtonType.PRIMARY}
              color={ButtonColor.GREEN_PRIMARY}
              size={ButtonSize.BASE}
              state={disabled ? ButtonState.DISABLED : ButtonState.DEFAULT}
              onClick={handleSave}
              loading={loading}
            >
              {saveButtonText || defaultButtonText}
            </Button>
          )}
        </div>
      </>
    );
  };

  return (
    <>
      <div
        className={classNames(styles.container, className, containerClassName, {
          [styles.mobile]: isMobile,
          [styles.noBorder]: hideBorder,
          [styles.compact]: compactMode,
        })}
      >
        {(title || defaultTitle) &&
          !hideTitle &&
          !(type === MultiFieldInputType.ADDRESS && mode === MultiFieldInputMode.MANUAL) && (
            <div
              className={classNames(styles.title, {
                [styles.addressTitle]:
                  type === MultiFieldInputType.ADDRESS && mode === MultiFieldInputMode.MANUAL,
              })}
            >
              {title || defaultTitle}
            </div>
          )}
        <div className={styles.inputContainer}>
          {mode === MultiFieldInputMode.SELECT ? renderSelectMode() : renderManualMode()}
        </div>
        {children}
      </div>

      {/* Phone Verification Modal */}
      <Modal
        open={isVerificationModalOpen}
        onClose={handleCloseVerificationModal}
        title={PHONE_VERIFICATION.VERIFICATION_TITLE}
        style={{ maxWidth: '400px', minWidth: 'inherit' }}
      >
        <div className={styles.verificationContainer}>
          <div className={styles.phoneVerificationInfo}>
            <p>
              <b>Enter the 6-digit code</b>
            </p>
            <p>Verification code sent to {phoneNumber}</p>
          </div>

          {renderVerificationCodeInput()}

          {verificationError && <div className={styles.verificationError}>{verificationError}</div>}
          <div className={styles.verificationActions}>
            <Button
              type={ButtonType.TERTIARY}
              color={ButtonColor.GREEN_PRIMARY}
              size={ButtonSize.L}
              state={
                resendCountdown && isResendDisabled ? ButtonState.DISABLED : ButtonState.DEFAULT
              }
              disabled={!!resendCountdown && !!isResendDisabled}
              onClick={handleSendVerificationCode}
            >
              {`${PHONE_VERIFICATION.RESEND_CODE}${resendCountdown ? ` (${resendCountdown})` : ''}`}
            </Button>
            <Button
              type={ButtonType.PRIMARY}
              color={ButtonColor.GREEN_PRIMARY}
              size={ButtonSize.L}
              state={verificationCode.length !== 6 ? ButtonState.DISABLED : ButtonState.DEFAULT}
              onClick={handleVerifyCode}
              loading={loading}
            >
              {PHONE_VERIFICATION.VERIFY_BUTTON}
            </Button>
            <Button
              type={ButtonType.TERTIARY}
              color={ButtonColor.GREEN_PRIMARY}
              size={ButtonSize.L}
              state={ButtonState.DEFAULT}
              onClick={handleCloseVerificationModal}
            >
              Edit phone number
            </Button>
          </div>
        </div>
      </Modal>

      {/* Email Verification Modal */}
      <Modal
        open={isEmailVerificationModalOpen}
        onClose={handleCloseEmailVerificationModal}
        title={EMAIL_VERIFICATION.VERIFICATION_TITLE}
        style={{ maxWidth: '400px', minWidth: 'inherit' }}
      >
        <div className={styles.verificationContainer}>
          <div className={styles.phoneVerificationInfo}>
            <p>
              <b>Enter the 6-digit code</b>
            </p>
            <p>Verification code sent to {emailAddress}</p>
          </div>

          {renderVerificationCodeInput()}

          {verificationError && <div className={styles.verificationError}>{verificationError}</div>}
          <div className={styles.verificationActions}>
            <Button
              type={ButtonType.TERTIARY}
              color={ButtonColor.GREEN_PRIMARY}
              size={ButtonSize.L}
              state={
                resendCountdown && isResendDisabled ? ButtonState.DISABLED : ButtonState.DEFAULT
              }
              disabled={!!resendCountdown && !!isResendDisabled}
              onClick={handleSendEmailVerificationCode}
            >
              {`${EMAIL_VERIFICATION.RESEND_CODE}${resendCountdown ? ` (${resendCountdown})` : ''}`}
            </Button>
            <Button
              type={ButtonType.PRIMARY}
              color={ButtonColor.GREEN_PRIMARY}
              size={ButtonSize.L}
              state={verificationCode.length !== 6 ? ButtonState.DISABLED : ButtonState.DEFAULT}
              onClick={handleVerifyEmailCode}
              loading={loading}
            >
              {EMAIL_VERIFICATION.VERIFY_BUTTON}
            </Button>
            <Button
              type={ButtonType.TERTIARY}
              color={ButtonColor.GREEN_PRIMARY}
              size={ButtonSize.L}
              state={ButtonState.DEFAULT}
              onClick={() => {
                clerk.openUserProfile();
              }}
            >
              Edit email address
            </Button>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default MultiFieldInput;
